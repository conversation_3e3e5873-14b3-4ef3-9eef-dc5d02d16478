export const authTranslations = {
  en: {
    nav: {
      home: "Home",
      subjects: "Subjects",
      teachers: "Teachers",
      pricing: "Pricing",
      contact: "Contact",
      login: "Login",
      signup: "Sign Up",
      welcome: "Welcome",
      profile: "Profile",
      settings: "Settings",
      logout: "Log out",
      dashboard: "Dashboard",
    },
    login: {
      title: "Welcome Back",
      subtitle: "Sign in to your Darasa account",
      email: "Email",
      emailPlaceholder: "Enter your email",
      password: "Password",
      passwordPlaceholder: "Enter your password",
      signInButton: "Sign In",
      signingIn: "Signing in...",
      noAccount: "Don't have an account?",
      signUpLink: "Sign up",
      forgotPassword: "Forgot your password?",
      loginSuccess: "Login successful! Welcome back.",
      loginFailed: "Login failed",
      validation: {
        emailRequired: "Please enter a valid email address",
        passwordRequired: "Password must be at least 6 characters",
      },
    },
    signup: {
      title: "Create Account",
      subtitle: "Join <PERSON> and start your learning journey",
      fullName: "Full Name",
      fullNamePlaceholder: "Enter your full name",
      email: "Email",
      emailPlaceholder: "Enter your email",
      phone: "Phone Number",
      phonePlaceholder: "Enter your phone number",
      role: "I am a",
      rolePlaceholder: "Select your role",
      gradeLevel: "Grade Level",
      gradeLevelPlaceholder: "Select your grade level",
      preferredLanguage: "Preferred Language",
      languagePlaceholder: "Select your language",
      password: "Password",
      passwordPlaceholder: "Create a password",
      confirmPassword: "Confirm Password",
      confirmPasswordPlaceholder: "Confirm your password",
      createAccountButton: "Create Account",
      creatingAccount: "Creating Account...",
      haveAccount: "Already have an account?",
      signInLink: "Sign in",
      roles: {
        student: "Student",
        teacher: "Teacher",
        parent: "Parent",
      },
      languages: {
        en: "English",
        ar: "Arabic",
      },
      grades: {
        "1": "Grade 1",
        "2": "Grade 2",
        "3": "Grade 3",
        "4": "Grade 4",
        "5": "Grade 5",
        "6": "Grade 6",
        "7": "Grade 7",
        "8": "Grade 8",
        "9": "Grade 9",
        "10": "Grade 10",
        "11": "Grade 11",
        "12": "Grade 12",
      },
      registrationSuccess: "Registration successful! Please check your email for the OTP code.",
      registrationFailed: "Registration failed",
      otpCodeInfo: "Your OTP code is: {{code}}",
      validation: {
        nameRequired: "Name must be at least 2 characters",
        emailRequired: "Please enter a valid email address",
        phoneRequired: "Phone number must be at least 10 digits",
        passwordRequired: "Password must be at least 6 characters",
        confirmPasswordRequired: "Please confirm your password",
        passwordMismatch: "Passwords don't match",
        roleRequired: "Please select your role",
        languageRequired: "Please select a language",
        gradeLevelRequired: "Grade level is required for students",
      },
    },
    otp: {
      title: "Verify Your Account",
      subtitle: "Enter the OTP code sent to your email",
      otpCode: "OTP Code",
      otpPlaceholder: "Enter 6-digit OTP code",
      verifyButton: "Verify Account",
      verifying: "Verifying...",
      verificationSuccess: "Account verified successfully! You can now log in.",
      verificationFailed: "OTP verification failed",
    },
  },
  ar: {
    nav: {
      home: "الرئيسية",
      subjects: "المواد",
      teachers: "المعلمون",
      pricing: "الأسعار",
      contact: "تواصل معنا",
      login: "تسجيل الدخول",
      signup: "إنشاء حساب",
      welcome: "مرحباً",
      profile: "الملف الشخصي",
      settings: "الإعدادات",
      logout: "تسجيل الخروج",
      dashboard: "لوحة التحكم",
    },
    login: {
      title: "مرحباً بعودتك",
      subtitle: "سجل دخولك إلى حساب درسة",
      email: "البريد الإلكتروني",
      emailPlaceholder: "أدخل بريدك الإلكتروني",
      password: "كلمة المرور",
      passwordPlaceholder: "أدخل كلمة المرور",
      signInButton: "تسجيل الدخول",
      signingIn: "جاري تسجيل الدخول...",
      noAccount: "ليس لديك حساب؟",
      signUpLink: "إنشاء حساب",
      forgotPassword: "نسيت كلمة المرور؟",
      loginSuccess: "تم تسجيل الدخول بنجاح! مرحباً بعودتك.",
      loginFailed: "فشل تسجيل الدخول",
      validation: {
        emailRequired: "يرجى إدخال عنوان بريد إلكتروني صحيح",
        passwordRequired: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
      },
    },
    signup: {
      title: "إنشاء حساب",
      subtitle: "انضم إلى درسة وابدأ رحلة التعلم",
      fullName: "الاسم الكامل",
      fullNamePlaceholder: "أدخل اسمك الكامل",
      email: "البريد الإلكتروني",
      emailPlaceholder: "أدخل بريدك الإلكتروني",
      phone: "رقم الهاتف",
      phonePlaceholder: "أدخل رقم هاتفك",
      role: "أنا",
      rolePlaceholder: "اختر دورك",
      gradeLevel: "المستوى الدراسي",
      gradeLevelPlaceholder: "اختر مستواك الدراسي",
      preferredLanguage: "اللغة المفضلة",
      languagePlaceholder: "اختر لغتك",
      password: "كلمة المرور",
      passwordPlaceholder: "أنشئ كلمة مرور",
      confirmPassword: "تأكيد كلمة المرور",
      confirmPasswordPlaceholder: "أكد كلمة المرور",
      createAccountButton: "إنشاء حساب",
      creatingAccount: "جاري إنشاء الحساب...",
      haveAccount: "لديك حساب بالفعل؟",
      signInLink: "تسجيل الدخول",
      roles: {
        student: "طالب",
        teacher: "معلم",
        parent: "ولي أمر",
      },
      languages: {
        en: "الإنجليزية",
        ar: "العربية",
      },
      grades: {
        "1": "الصف الأول",
        "2": "الصف الثاني",
        "3": "الصف الثالث",
        "4": "الصف الرابع",
        "5": "الصف الخامس",
        "6": "الصف السادس",
        "7": "الصف السابع",
        "8": "الصف الثامن",
        "9": "الصف التاسع",
        "10": "الصف العاشر",
        "11": "الصف الحادي عشر",
        "12": "الصف الثاني عشر",
      },
      registrationSuccess: "تم التسجيل بنجاح! يرجى التحقق من بريدك الإلكتروني للحصول على رمز التحقق.",
      registrationFailed: "فشل التسجيل",
      otpCodeInfo: "رمز التحقق الخاص بك هو: {{code}}",
      validation: {
        nameRequired: "يجب أن يكون الاسم حرفين على الأقل",
        emailRequired: "يرجى إدخال عنوان بريد إلكتروني صحيح",
        phoneRequired: "يجب أن يكون رقم الهاتف 10 أرقام على الأقل",
        passwordRequired: "يجب أن تكون كلمة المرور 6 أحرف على الأقل",
        confirmPasswordRequired: "يرجى تأكيد كلمة المرور",
        passwordMismatch: "كلمات المرور غير متطابقة",
        roleRequired: "يرجى اختيار دورك",
        languageRequired: "يرجى اختيار لغة",
        gradeLevelRequired: "المستوى الدراسي مطلوب للطلاب",
      },
    },
    otp: {
      title: "تحقق من حسابك",
      subtitle: "أدخل رمز التحقق المرسل إلى بريدك الإلكتروني",
      otpCode: "رمز التحقق",
      otpPlaceholder: "أدخل رمز التحقق المكون من 6 أرقام",
      verifyButton: "تحقق من الحساب",
      verifying: "جاري التحقق...",
      verificationSuccess: "تم التحقق من الحساب بنجاح! يمكنك الآن تسجيل الدخول.",
      verificationFailed: "فشل التحقق من رمز التحقق",
    },
  },
};
