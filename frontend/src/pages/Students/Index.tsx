import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Book, Bell, User, Clock, AlertCircle, MessageSquare, Calendar, Upload, LogOut } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageToggle from "@/components/LanguageToggle";
import CalendarComponent from "@/components/CalendarComponent";
import TeachersList from "@/components/TeachersList";
import FileUpload from "@/components/FileUpload";
// Mock data for teachers
const teachers = [
  { id: 1, name: "Dr<PERSON> <PERSON>", subject: "Mathematics", email: "<EMAIL>" },
  { id: 2, name: "<PERSON><PERSON>", subject: "Computer Science", email: "<EMAIL>" },
  { id: 3, name: "<PERSON><PERSON> <PERSON>", subject: "Physics", email: "<EMAIL>" },
  { id: 4, name: "<PERSON><PERSON> <PERSON>", subject: "Literature", email: "<EMAIL>" },
];

// Mock data for scheduled classes
const scheduledClasses = [
  { id: 1, title: "Advanced Mathematics", date: "2024-05-15T10:00:00", duration: 60, teacher: "Dr. <PERSON>" },
  { id: 2, title: "Computer Science", date: "2024-05-16T14:00:00", duration: 90, teacher: "Ms. <PERSON>" },
  { id: 3, title: "Physics", date: "2024-05-17T09:00:00", duration: 60, teacher: "Dr. <PERSON>" },
  { id: 4, title: "Literature", date: "2024-05-19T11:00:00", duration: 90, teacher: "Prof. Davis" },
];

interface StatsCardProps {
  title: string;
  value: string;
  subtitle: string;
  bgColor: string;
  textColor: string;
}

const StatsCard = ({ title, value, subtitle, bgColor, textColor }: StatsCardProps) => {
  return (
    <div className={`p-6 rounded-xl ${bgColor}`}>
      <h3 className="text-sm font-medium text-gray-500 mb-1">{title}</h3>
      <p className={`text-2xl font-semibold ${textColor} mb-1`}>{value}</p>
      <p className="text-xs text-gray-500">{subtitle}</p>
    </div>
  );
};

interface CourseCardProps {
  id: number;
  title: string;
  instructor: string;
  progress: number;
  nextClass: string;
  color: string;
  onClick: (id: number) => void;
}

const CourseCard = ({ id, title, instructor, progress, nextClass, color, onClick }: CourseCardProps) => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-100 cursor-pointer" onClick={() => onClick(id)}>
      <div className="flex items-start justify-between mb-4">
        <div className={`p-3 ${color} rounded-lg`}>
          <Book className="h-6 w-6 text-white" />
        </div>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{progress}% complete</span>
      </div>
      <h3 className="font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-3">Prof. {instructor}</p>
      <div className="space-y-2">
        <div className="flex justify-between text-xs text-gray-500">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className={`h-2 rounded-full ${color}`} style={{ width: `${progress}%` }}></div>
        </div>
        <p className="text-xs text-gray-500 mt-3">Next: {nextClass}</p>
      </div>
    </div>
  );
};

interface AssignmentItemProps {
  id: number;
  title: string;
  course: string;
  dueDate: string;
  priority: "high" | "medium" | "low";
  status: "pending" | "submitted" | "graded";
  onUpload: (id: number) => void;
}

const AssignmentItem = ({ id, title, course, dueDate, priority, status, onUpload }: AssignmentItemProps) => {
  const priorityColors = {
    high: "text-red-600 bg-red-50",
    medium: "text-yellow-600 bg-yellow-50",
    low: "text-green-600 bg-green-50",
  };

  const statusColors = {
    pending: "text-orange-600 bg-orange-50",
    submitted: "text-blue-600 bg-blue-50",
    graded: "text-green-600 bg-green-50",
  };

  return (
    <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-gray-50 rounded-lg">{priority === "high" ? <AlertCircle className="h-4 w-4 text-red-500" /> : <Clock className="h-4 w-4 text-gray-500" />}</div>
        <div>
          <h4 className="font-medium text-gray-900 text-sm">{title}</h4>
          <p className="text-xs text-gray-500">{course}</p>
        </div>
      </div>
      <div className="flex items-center space-x-3">
        <span className={`text-xs px-2 py-1 rounded-full ${priorityColors[priority]}`}>{priority}</span>
        <span className={`text-xs px-2 py-1 rounded-full ${statusColors[status]}`}>{status}</span>
        <span className="text-xs text-gray-500">{dueDate}</span>
        {status === "pending" && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUpload(id);
            }}
            className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200">
            <Upload className="h-3 w-3 inline mr-1" />
            Upload
          </button>
        )}
      </div>
    </div>
  );
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DashboardHeader = ({ onLogout, user }: { onLogout: () => void; user: any }) => {
  const { language, setLanguage, isRTL } = useLanguage();
  const content = {
    en: {
      title: "Student Dashboard",
      welcome: `Welcome back, ${user?.name || "Student"}!`,
      notifications: "Notifications",
      logout: "Logout",
    },
    ar: {
      title: "لوحة الطالب",
      welcome: `مرحباً بعودتك، ${user?.name || "طالب"}!`,
      notifications: "الإشعارات",
      logout: "تسجيل الخروج",
    },
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Book className="h-6 w-6 text-blue-600" />
          </div>
          <div className={isRTL ? "text-right" : "text-left"}>
            <h1 className="text-xl font-semibold text-gray-900">{content[language].title}</h1>
            <p className="text-sm text-gray-600">{content[language].welcome}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <LanguageToggle language={language} onLanguageChange={setLanguage} />
          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Bell className="h-5 w-5" />
            <span className="sr-only">{content[language].notifications}</span>
          </button>
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-gray-100 rounded-full">
              <User className="h-5 w-5 text-gray-600" />
            </div>
            <span className="text-sm font-medium text-gray-700">{user?.name || "Student"}</span>
          </div>
          <button onClick={onLogout} className="flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors" title={content[language].logout}>
            <LogOut className="h-4 w-4" />
            <span className="hidden sm:inline">{content[language].logout}</span>
          </button>
        </div>
      </div>
    </header>
  );
};

const StudentDashboard = () => {
  const [activeView, setActiveView] = useState<"dashboard" | "schedule" | "teachers" | "upload">("dashboard");
  const [selectedAssignment, setSelectedAssignment] = useState<number | null>(null);
  const navigate = useNavigate();
  const { logout, user } = useAuth();
  const { language, setLanguage, isRTL } = useLanguage();

  const content = {
    en: {
      stats: {
        courses: "Total Courses",
        assignments: "Assignments Due",
        gpa: "Overall GPA",
        hours: "Study Hours",
        enrolled: "Currently enrolled",
        thisWeek: "This week",
        lastSemester: "Last semester",
      },
      sections: {
        courses: "My Courses",
        assignments: "Recent Assignments",
        quickActions: "Quick Actions",
        submitAssignment: "Submit Assignment",
        viewSchedule: "View Schedule",
        contactTeachers: "Contact Teachers",
      },
      priority: {
        high: "high",
        medium: "medium",
        low: "low",
      },
      status: {
        pending: "pending",
        submitted: "submitted",
        graded: "graded",
      },
    },
    ar: {
      stats: {
        courses: "إجمالي المواد",
        assignments: "واجبات مستحقة",
        gpa: "المعدل التراكمي",
        hours: "ساعات الدراسة",
        enrolled: "مسجل حالياً",
        thisWeek: "هذا الأسبوع",
        lastSemester: "الفصل الماضي",
      },
      sections: {
        courses: "موادي الدراسية",
        assignments: "الواجبات الأخيرة",
        quickActions: "إجراءات سريعة",
        submitAssignment: "تسليم واجب",
        viewSchedule: "عرض الجدول",
        contactTeachers: "تواصل مع المعلمين",
      },
      priority: {
        high: "عالي",
        medium: "متوسط",
        low: "منخفض",
      },
      status: {
        pending: "قيد الانتظار",
        submitted: "تم التسليم",
        graded: "تم التصحيح",
      },
    },
  };

  const courses = [
    {
      id: 1,
      title: language === "en" ? "Advanced Mathematics" : "الرياضيات المتقدمة",
      instructor: language === "en" ? "Dr. Smith" : "د. أحمد",
      progress: 75,
      nextClass: language === "en" ? "Tomorrow 10:00 AM" : "غداً 10:00 ص",
      color: "bg-blue-500",
    },
    {
      id: 2,
      title: language === "en" ? "Computer Science" : "علوم الحاسوب",
      instructor: language === "en" ? "Ms. Johnson" : "أ. سارة",
      progress: 60,
      nextClass: language === "en" ? "Wed 2:00 PM" : "الأربعاء 2:00 م",
      color: "bg-green-500",
    },
    {
      id: 3,
      title: language === "en" ? "Physics" : "الفيزياء",
      instructor: language === "en" ? "Dr. Brown" : "د. خالد",
      progress: 45,
      nextClass: language === "en" ? "Thu 9:00 AM" : "الخميس 9:00 ص",
      color: "bg-purple-500",
    },
    {
      id: 4,
      title: language === "en" ? "Literature" : "الأدب",
      instructor: language === "en" ? "Prof. Davis" : "د. فاطمة",
      progress: 80,
      nextClass: language === "en" ? "Fri 11:00 AM" : "الجمعة 11:00 ص",
      color: "bg-pink-500",
    },
  ];

  const assignments = [
    {
      id: 1,
      title: language === "en" ? "Calculus Problem Set 3" : "مجموعة مسائل التفاضل 3",
      course: language === "en" ? "Advanced Mathematics" : "الرياضيات المتقدمة",
      dueDate: language === "en" ? "Today" : "اليوم",
      priority: "high" as const,
      status: "pending" as const,
    },
    {
      id: 2,
      title: language === "en" ? "Programming Project" : "مشروع البرمجة",
      course: language === "en" ? "Computer Science" : "علوم الحاسوب",
      dueDate: language === "en" ? "Tomorrow" : "غداً",
      priority: "medium" as const,
      status: "pending" as const,
    },
    {
      id: 3,
      title: language === "en" ? "Essay: Shakespeare Analysis" : "تحليل مقالة شكسبير",
      course: language === "en" ? "Literature" : "الأدب",
      dueDate: language === "en" ? "3 days" : "3 أيام",
      priority: "low" as const,
      status: "submitted" as const,
    },
    {
      id: 4,
      title: language === "en" ? "Lab Report" : "تقرير المختبر",
      course: language === "en" ? "Physics" : "الفيزياء",
      dueDate: language === "en" ? "1 week" : "أسبوع",
      priority: "medium" as const,
      status: "graded" as const,
    },
  ];

  const handleCourseClick = (id: number) => {
    navigate(`/course/${id}`);
  };

  const handleAssignmentUpload = (id: number) => {
    setSelectedAssignment(id);
    setActiveView("upload");
  };

  const handleFileSubmit = (file: File) => {
    // In a real app, you would send this file to the server
    console.log(`Submitting file for assignment ${selectedAssignment}:`, file.name);
    // Update the assignment status
    const assignmentIndex = assignments.findIndex((a) => a.id === selectedAssignment);
    if (assignmentIndex !== -1) {
      // Update status to submitted
      console.log(`Assignment ${selectedAssignment} submitted successfully`);
    }
    setSelectedAssignment(null);
    setActiveView("dashboard");
  };

  const handleMessageTeacher = (teacherId: number) => {
    console.log(`Messaging teacher ${teacherId}`);
    // In a real app, this would open a chat interface
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      <DashboardHeader onLogout={logout} user={user} />

      {activeView === "dashboard" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatsCard title={content[language].stats.courses} value="4" subtitle={content[language].stats.enrolled} bgColor="bg-blue-50" textColor="text-blue-600" />
            <StatsCard title={content[language].stats.assignments} value="2" subtitle={content[language].stats.thisWeek} bgColor="bg-yellow-50" textColor="text-yellow-600" />
            <StatsCard title={content[language].stats.gpa} value="3.8" subtitle={content[language].stats.lastSemester} bgColor="bg-green-50" textColor="text-green-600" />
            <StatsCard title={content[language].stats.hours} value="24" subtitle={content[language].stats.thisWeek} bgColor="bg-purple-50" textColor="text-purple-600" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Courses Section */}
            <div className="lg:col-span-2">
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">{content[language].sections.courses}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {courses.map((course) => (
                    <CourseCard key={course.id} id={course.id} title={course.title} instructor={course.instructor} progress={course.progress} nextClass={course.nextClass} color={course.color} onClick={handleCourseClick} />
                  ))}
                </div>
              </div>
            </div>

            {/* Assignments Section */}
            <div className="lg:col-span-1">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{content[language].sections.assignments}</h2>
              <div className="space-y-3">
                {assignments.map((assignment) => (
                  <AssignmentItem key={assignment.id} id={assignment.id} title={assignment.title} course={assignment.course} dueDate={assignment.dueDate} priority={assignment.priority} status={assignment.status} onUpload={handleAssignmentUpload} />
                ))}
              </div>

              {/* Quick Actions */}
              <div className="mt-6 p-4 bg-white rounded-xl shadow-sm border border-gray-100">
                <h3 className="font-medium text-gray-900 mb-3">{content[language].sections.quickActions}</h3>
                <div className="space-y-2">
                  <button className="w-full flex items-center p-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors" onClick={() => setActiveView("upload")}>
                    <Upload className="h-4 w-4 mr-2" />
                    {content[language].sections.submitAssignment}
                  </button>
                  <button className="w-full flex items-center p-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors" onClick={() => setActiveView("schedule")}>
                    <Calendar className="h-4 w-4 mr-2" />
                    {content[language].sections.viewSchedule}
                  </button>
                  <button className="w-full flex items-center p-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors" onClick={() => setActiveView("teachers")}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {content[language].sections.contactTeachers}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeView === "schedule" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">{language === "en" ? "My Class Schedule" : "جدول الحصص"}</h2>
            <Button variant="outline" onClick={() => setActiveView("dashboard")}>
              {language === "en" ? "Back to Dashboard" : "العودة للوحة التحكم"}
            </Button>
          </div>
          <CalendarComponent events={scheduledClasses} language={language} />
        </div>
      )}

      {activeView === "teachers" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">{language === "en" ? "My Teachers" : "معلموني"}</h2>
            <Button variant="outline" onClick={() => setActiveView("dashboard")}>
              {language === "en" ? "Back to Dashboard" : "العودة للوحة التحكم"}
            </Button>
          </div>
          <TeachersList teachers={teachers} language={language} onMessage={handleMessageTeacher} />
        </div>
      )}

      {activeView === "upload" && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">{language === "en" ? "Submit Assignment" : "تسليم الواجب"}</h2>
            <Button variant="outline" onClick={() => setActiveView("dashboard")}>
              {language === "en" ? "Back to Dashboard" : "العودة للوحة التحكم"}
            </Button>
          </div>
          <FileUpload onFileSubmit={handleFileSubmit} language={language} assignment={assignments.find((a) => a.id === selectedAssignment)} />
        </div>
      )}
    </div>
  );
};

export default StudentDashboard;
