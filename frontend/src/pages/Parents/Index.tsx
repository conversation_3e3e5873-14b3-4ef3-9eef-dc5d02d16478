import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, Clock, CheckCircle, AlertCircle, BarChart3, MessageSquare, Calendar, Plus } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const ParentDashboard = () => {
  const navigate = useNavigate();
  const { language, setLanguage, isRTL } = useLanguage();

  const content = {
    ar: {
      title: "لوحة ولي الأمر",
      subtitle: "مراقبة تقدم أبنائك التعليمي",
      children: {
        title: "أبنائي",
        grade: "الصف",
        avgGrade: "المعدل",
        sessionProgress: "تقدم الجلسات",
        subjects: "المواد",
        viewReport: "عرض التقرير المفصل",
      },
      approvals: {
        title: "جلسات تحتاج الموافقة",
        child: "الابن",
        subject: "المادة",
        with: "مع",
        date: "التاريخ",
        duration: "المدة",
        decline: "رفض",
        approve: "موافقة",
      },
      reports: {
        title: "تقارير التقدم الأخيرة",
        feedback: "ملاحظات",
        grade: "التقدير",
      },
      stats: {
        title: "نظرة عامة على العائلة",
        activeChildren: "الأبناء النشطين",
        totalSessions: "إجمالي الجلسات",
        avgPerformance: "متوسط الأداء",
      },
      schedule: {
        title: "جدول هذا الأسبوع",
        upcoming: "القادمة",
      },
      actions: {
        title: "إجراءات سريعة",
        messageTeachers: "مراسلة المعلمين",
        viewReports: "عرض التقارير الكاملة",
        scheduleSession: "جدولة جلسة جديدة",
      },
      nav: {
        profile: "الملف الشخصي",
        logout: "تسجيل الخروج",
      },
    },
    en: {
      title: "Parent Dashboard",
      subtitle: "Monitor your children's progress",
      children: {
        title: "My Children",
        grade: "Grade",
        avgGrade: "Average",
        sessionProgress: "Session Progress",
        subjects: "Subjects",
        viewReport: "View Detailed Report",
      },
      approvals: {
        title: "Pending Session Approvals",
        child: "Child",
        subject: "Subject",
        with: "with",
        date: "Date",
        duration: "Duration",
        decline: "Decline",
        approve: "Approve",
      },
      reports: {
        title: "Recent Progress Reports",
        feedback: "Feedback",
        grade: "Grade",
      },
      stats: {
        title: "Family Overview",
        activeChildren: "Active Children",
        totalSessions: "Total Sessions",
        avgPerformance: "Average Performance",
      },
      schedule: {
        title: "This Week's Schedule",
        upcoming: "Upcoming",
      },
      actions: {
        title: "Quick Actions",
        messageTeachers: "Message Teachers",
        viewReports: "View Full Reports",
        scheduleSession: "Schedule New Session",
      },
      nav: {
        profile: "Profile",
        logout: "Logout",
      },
    },
  };

  const t = content[language];

  const children = [
    {
      name: language === "ar" ? "أحمد محمد" : "Ahmed Mohammed",
      grade: language === "ar" ? "الصف العاشر" : "Grade 10",
      totalSessions: 24,
      completedSessions: 20,
      averageGrade: 85,
      subjects: language === "ar" ? ["رياضيات", "فيزياء", "كيمياء"] : ["Math", "Physics", "Chemistry"],
    },
    {
      name: language === "ar" ? "فاطمة محمد" : "Fatima Mohammed",
      grade: language === "ar" ? "الصف الثامن" : "Grade 8",
      totalSessions: 16,
      completedSessions: 14,
      averageGrade: 92,
      subjects: language === "ar" ? ["إنجليزي", "عربي", "تاريخ"] : ["English", "Arabic", "History"],
    },
  ];

  const pendingApprovals = [
    {
      child: language === "ar" ? "أحمد محمد" : "Ahmed Mohammed",
      teacher: language === "ar" ? "د. سارة أحمد" : "Dr. Sara Ahmed",
      subject: language === "ar" ? "رياضيات" : "Mathematics",
      date: language === "ar" ? "اليوم، 4:00 مساءً" : "Today, 4:00 PM",
      duration: language === "ar" ? "ساعة واحدة" : "1 hour",
      cost: language === "ar" ? "25,000 د.ع" : "$25",
    },
    {
      child: language === "ar" ? "فاطمة محمد" : "Fatima Mohammed",
      teacher: language === "ar" ? "أستاذة ليلى علي" : "Ms. Leila Ali",
      subject: language === "ar" ? "إنجليزي" : "English",
      date: language === "ar" ? "غداً، 3:00 مساءً" : "Tomorrow, 3:00 PM",
      duration: language === "ar" ? "45 دقيقة" : "45 mins",
      cost: language === "ar" ? "20,000 د.ع" : "$20",
    },
  ];

  const recentReports = [
    {
      child: language === "ar" ? "أحمد محمد" : "Ahmed Mohammed",
      subject: language === "ar" ? "فيزياء" : "Physics",
      teacher: language === "ar" ? "د. يوسف حسن" : "Dr. Youssef Hassan",
      date: language === "ar" ? "أمس" : "Yesterday",
      grade: "A-",
      feedback: language === "ar" ? "تقدم ممتاز في الميكانيكا. يحتاج إلى مزيد من التدريب على الديناميكا الحرارية." : "Excellent progress in mechanics. Needs more practice with thermodynamics.",
    },
    {
      child: language === "ar" ? "فاطمة محمد" : "Fatima Mohammed",
      subject: language === "ar" ? "إنجليزي" : "English",
      teacher: language === "ar" ? "أستاذة ليلى علي" : "Ms. Leila Ali",
      date: language === "ar" ? "قبل يومين" : "2 days ago",
      grade: "A+",
      feedback: language === "ar" ? "أداء متميز في الكتابة. تحسن كبير في القواعد." : "Outstanding performance in writing. Great improvement in grammar.",
    },
  ];

  const weeklySchedule = [
    {
      child: language === "ar" ? "أحمد" : "Ahmed",
      subject: language === "ar" ? "رياضيات" : "Math",
      time: language === "ar" ? "اليوم، 4:00 مساءً" : "Today, 4:00 PM",
      status: "pending",
    },
    {
      child: language === "ar" ? "فاطمة" : "Fatima",
      subject: language === "ar" ? "إنجليزي" : "English",
      time: language === "ar" ? "غداً، 3:00 مساءً" : "Tomorrow, 3:00 PM",
      status: "confirmed",
    },
    {
      child: language === "ar" ? "أحمد" : "Ahmed",
      subject: language === "ar" ? "فيزياء" : "Physics",
      time: language === "ar" ? "الجمعة، 2:00 مساءً" : "Friday, 2:00 PM",
      status: "upcoming",
    },
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`} dir={isRTL ? "rtl" : "ltr"}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Children Overview */}
            <div className="grid md:grid-cols-2 gap-6">
              {children.map((child, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className={language === "ar" ? "text-right" : "text-left"}>
                        <h3 className="font-semibold">{child.name}</h3>
                        <p className="text-sm text-gray-600 font-normal">{child.grade}</p>
                      </div>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-600">
                        {child.averageGrade}% {t.children.avgGrade}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>{t.children.sessionProgress}</span>
                          <span>
                            {child.completedSessions}/{child.totalSessions}
                          </span>
                        </div>
                        <Progress value={(child.completedSessions / child.totalSessions) * 100} className="h-2" />
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">{t.children.subjects}:</p>
                        <div className="flex flex-wrap gap-2">
                          {child.subjects.map((subject, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {subject}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <Button variant="outline" size="sm" className="w-full">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        {t.children.viewReport}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pending Approvals */}
            <Card>
              <CardHeader className="bg-orange-50">
                <CardTitle className="flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
                  {t.approvals.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pendingApprovals.map((approval, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{approval.child}</h4>
                        <p className="text-sm text-gray-600">
                          {approval.subject} {t.approvals.with} {approval.teacher}
                        </p>
                        <p className="text-sm text-gray-500">
                          {approval.date} • {approval.duration}
                        </p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="font-semibold text-green-600">{approval.cost}</span>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            {t.approvals.decline}
                          </Button>
                          <Button size="sm" className="bg-green-600 hover:bg-green-700">
                            {t.approvals.approve}
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Reports */}
            <Card>
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                  {t.reports.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentReports.map((report, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{report.child}</h4>
                          <p className="text-sm text-gray-600">
                            {report.subject} • {report.teacher}
                          </p>
                          <p className="text-xs text-gray-500">{report.date}</p>
                        </div>
                        <Badge variant="secondary" className={`${report.grade.startsWith("A") ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}`}>
                          {report.grade}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded italic">"{report.feedback}"</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader className="bg-blue-50">
                <CardTitle>{t.stats.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">{t.stats.activeChildren}</span>
                    <span className="text-2xl font-bold text-yellow-500">{children.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">{t.stats.totalSessions}</span>
                    <span className="text-2xl font-bold text-green-600">{children.reduce((sum, child) => sum + child.totalSessions, 0)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">{t.stats.avgPerformance}</span>
                    <span className="text-2xl font-bold text-blue-600">{Math.round(children.reduce((sum, child) => sum + child.averageGrade, 0) / children.length)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* This Week's Schedule */}
            <Card>
              <CardHeader className="bg-blue-50">
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  {t.schedule.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {weeklySchedule.map((session, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                      <div className={`w-3 h-3 rounded-full ${session.status === "pending" ? "bg-yellow-500" : session.status === "confirmed" ? "bg-green-500" : "bg-blue-500"}`}></div>
                      <div className={language === "ar" ? "text-right" : "text-left"}>
                        <p className="font-medium text-sm">
                          {session.child} - {session.subject}
                        </p>
                        <p className="text-xs text-gray-600">{session.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader className="bg-blue-50">
                <CardTitle>{t.actions.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {t.actions.messageTeachers}
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    {t.actions.viewReports}
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="h-4 w-4 mr-2" />
                    {t.actions.scheduleSession}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParentDashboard;
